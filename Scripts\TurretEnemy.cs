using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace HELLSTRIKE
{
    [RequireComponent(typeof(Animator))]
    [RequireComponent(typeof(Rigidbody))]
    [RequireComponent(typeof(Collider))]
    public class TurretEnemy : MonoBehaviour
    {
        [Space(10)]
        [Header("Health Settings")]
        [SerializeField] private float maxHealth = 100f;
        private float currentHealth;
        private bool isDead = false;

        [Space(10)]
        [Header("Audio Settings")]
        [SerializeField] private AudioClip shootSound;
        [SerializeField] [Range(0f, 1f)] private float shootSoundVolume = 1f;
        [SerializeField] private AudioClip detectionSound;
        [SerializeField] [Range(0f, 1f)] private float detectionSoundVolume = 0.8f;
        [SerializeField] private AudioClip deathSound;
        [SerializeField] [Range(0f, 1f)] private float deathSoundVolume = 1f;

        private AudioSource shootAudioSource;
        private AudioSource detectionAudioSource;
        private AudioSource deathAudioSource;

        Animator animator;
        Rigidbody rb;
        BloodSystem bloodSystem;

        [Space(10)]
        [Header("Detection Settings")]
        [Tooltip("Detection radius for finding the player")]
        [SerializeField] private float detectionRadius = 15f;
        [Tooltip("Attack range - how far turret can shoot")]
        [SerializeField] private float attackRange = 12f;
        [SerializeField] private LayerMask playerLayer;

        [Space(10)]
        [Header("Turret Rotation")]
        [Tooltip("How fast the turret rotates to track player")]
        [SerializeField] private float rotationSpeed = 90f;
        [Tooltip("If true, turret can rotate vertically (up/down). If false, only horizontal rotation")]
        [SerializeField] private bool allowVerticalRotation = true;
        [Tooltip("Vertical rotation limits (min, max angles) - only used if allowVerticalRotation is true")]
        [SerializeField] private Vector2 verticalRotationLimits = new Vector2(-30f, 45f);

        [Space(10)]
        [Header("Shooting Settings")]
        [Tooltip("Number of projectiles to fire in burst")]
        [SerializeField] private int burstCount = 3;
        [Tooltip("Time between shots in burst")]
        [SerializeField] private float timeBetweenBurstShots = 0.2f;
        [Tooltip("Time between bursts")]
        [SerializeField] private float timeBetweenBursts = 2f;
        [Tooltip("Damage per projectile")]
        [SerializeField] private float damagePerProjectile = 15f;

        [Space(10)]
        [Header("Projectile Settings")]
        [Tooltip("Transform where projectiles spawn from (adjustable origin)")]
        [SerializeField] private Transform projectileSpawnPoint;
        [Tooltip("Speed of Projectile being fired")]
        [Range(5.0f, 25.0f)]
        [SerializeField] private float projectileForce = 15f;
        [Tooltip("Size of the projectile (sphere radius)")]
        [Range(0.05f, 0.5f)]
        [SerializeField] private float projectileSize = 0.1f;
        [Tooltip("How long projectiles exist before auto-destroying")]
        [Range(1f, 10f)]
        [SerializeField] private float projectileLifetime = 5f;
        [Tooltip("Material for projectile appearance (optional)")]
        [SerializeField] private Material projectileMaterial;

        [Space(10)]
        [Header("Death Effects")]
        [Tooltip("Particle effect prefab to spawn when turret dies")]
        [SerializeField] private GameObject deathEffectPrefab;

        // Private variables
        private Transform player;
        private bool isAttacking = false;
        private bool playerInRange = false;
        private float lastBurstTime = 0f;
        private Quaternion originalRotation;

        void Start()
        {
            // Set references to Animator and Rigidbody Component
            animator = GetComponent<Animator>();
            rb = GetComponent<Rigidbody>();

            // Sets Rigidbody to work as stationary turret
            rb.isKinematic = true;
            rb.collisionDetectionMode = CollisionDetectionMode.Continuous;

            // Initialize health
            currentHealth = maxHealth;

            // Setup audio sources
            SetupAudioSources();

            // Store original rotation
            originalRotation = transform.rotation;

            // Find the player
            GameObject playerObject = GameObject.FindGameObjectWithTag("Player");
            if (playerObject != null)
            {
                player = playerObject.transform;
                Debug.Log($"TurretEnemy: Found player at {player.position}");
            }
            else
            {
                Debug.LogWarning("TurretEnemy: No GameObject with 'Player' tag found!");
            }

            // Find BloodSystem
            bloodSystem = FindFirstObjectByType<BloodSystem>();
            if (bloodSystem == null)
            {
                Debug.LogWarning("TurretEnemy: No BloodSystem found in scene!");
            }

            // Start player detection
            if (player != null)
            {
                StartCoroutine(CheckForPlayer());
            }

            Debug.Log($"TurretEnemy: Initialized with {burstCount} shot bursts, {detectionRadius}m detection range");
        }

        void Update()
        {
            if (isDead) return;

            // Rotate turret to track player if in range
            if (playerInRange && player != null)
            {
                RotateTurretTowardsPlayer();
            }
        }

        private void RotateTurretTowardsPlayer()
        {
            Vector3 directionToPlayer = (player.position - transform.position).normalized;

            // Calculate target rotation
            Quaternion targetRotation = Quaternion.LookRotation(directionToPlayer);

            if (allowVerticalRotation)
            {
                // Apply vertical rotation limits
                Vector3 eulerAngles = targetRotation.eulerAngles;
                float xAngle = eulerAngles.x;

                // Convert to -180 to 180 range
                if (xAngle > 180f) xAngle -= 360f;

                // Clamp the vertical rotation
                xAngle = Mathf.Clamp(xAngle, verticalRotationLimits.x, verticalRotationLimits.y);
                eulerAngles.x = xAngle;

                targetRotation = Quaternion.Euler(eulerAngles);
            }
            else
            {
                // Only horizontal rotation - keep current X and Z rotation, only change Y
                Vector3 eulerAngles = targetRotation.eulerAngles;
                eulerAngles.x = transform.eulerAngles.x;
                eulerAngles.z = transform.eulerAngles.z;
                targetRotation = Quaternion.Euler(eulerAngles);
            }

            // Smoothly rotate towards target
            transform.rotation = Quaternion.RotateTowards(transform.rotation, targetRotation, rotationSpeed * Time.deltaTime);
        }

        private IEnumerator CheckForPlayer()
        {
            while (player != null && !isDead)
            {
                float distanceToPlayer = Vector3.Distance(transform.position, player.position);

                // Check if player is within detection radius
                if (distanceToPlayer <= detectionRadius)
                {
                    if (!playerInRange)
                    {
                        playerInRange = true;
                        PlayDetectionSound();
                        Debug.Log($"TurretEnemy: Player detected! Distance: {distanceToPlayer:F2}");
                    }

                    // Check if close enough to attack and enough time has passed since last burst
                    if (distanceToPlayer <= attackRange && !isAttacking && Time.time >= lastBurstTime + timeBetweenBursts)
                    {
                        Debug.Log($"TurretEnemy: Starting burst attack! Distance to player: {distanceToPlayer:F2}");
                        StartCoroutine(FireBurst());
                    }
                }
                else if (playerInRange)
                {
                    // Player is out of range
                    playerInRange = false;
                    Debug.Log($"TurretEnemy: Lost player. Distance: {distanceToPlayer:F2}");
                }

                yield return new WaitForSeconds(0.1f); // Check more frequently for responsive turret
            }
        }

        private IEnumerator FireBurst()
        {
            if (isAttacking) yield break;

            isAttacking = true;
            lastBurstTime = Time.time;

            // Trigger attack animation
            if (animator != null)
                animator.SetTrigger("Attack");

            // Fire burst of projectiles
            for (int i = 0; i < burstCount; i++)
            {
                FireProjectile();
                
                // Wait between shots in burst (except for last shot)
                if (i < burstCount - 1)
                {
                    yield return new WaitForSeconds(timeBetweenBurstShots);
                }
            }

            isAttacking = false;
        }

        private void FireProjectile()
        {
            Debug.Log($"TurretEnemy: Firing projectile!");

            if (projectileSpawnPoint != null)
            {
                // Create projectile GameObject
                GameObject projectileObj = CreateProjectile();

                if (projectileObj != null)
                {
                    // Position and orient the projectile
                    projectileObj.transform.position = projectileSpawnPoint.position;
                    projectileObj.transform.rotation = projectileSpawnPoint.rotation;

                    // Get the rigidbody and add force
                    Rigidbody projectileRb = projectileObj.GetComponent<Rigidbody>();
                    if (projectileRb != null)
                    {
                        projectileRb.AddForce(projectileSpawnPoint.forward * projectileForce, ForceMode.Impulse);
                    }

                    // Ignore collisions between turret and projectile
                    Collider projectileCollider = projectileObj.GetComponent<Collider>();
                    Collider turretCollider = gameObject.GetComponent<Collider>();
                    if (projectileCollider != null && turretCollider != null)
                    {
                        Physics.IgnoreCollision(turretCollider, projectileCollider, true);
                    }

                    // Play shoot sound
                    PlayShootSound();
                }
            }
            else
            {
                Debug.LogWarning("TurretEnemy: Missing Projectile Spawn Point!");
            }
        }

        private GameObject CreateProjectile()
        {
            // Create the projectile GameObject
            GameObject projectile = GameObject.CreatePrimitive(PrimitiveType.Sphere);
            projectile.name = "TurretProjectile";

            // Scale the projectile
            projectile.transform.localScale = Vector3.one * projectileSize;

            // Add Rigidbody
            Rigidbody rb = projectile.AddComponent<Rigidbody>();
            rb.mass = 0.1f;
            rb.drag = 0f;
            rb.angularDrag = 0f;

            // Setup Collider (use the default sphere collider from CreatePrimitive)
            SphereCollider collider = projectile.GetComponent<SphereCollider>();
            collider.isTrigger = true; // Use trigger for better hit detection

            // Add TurretProjectile script
            TurretProjectile projectileScript = projectile.AddComponent<TurretProjectile>();
            projectileScript.SetDamage(damagePerProjectile);
            projectileScript.SetLifetime(projectileLifetime);

            // Apply material if provided
            if (projectileMaterial != null)
            {
                Renderer renderer = projectile.GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material = projectileMaterial;
                }
            }
            else
            {
                // Create a simple red material for visibility
                Renderer renderer = projectile.GetComponent<Renderer>();
                if (renderer != null)
                {
                    Material defaultMaterial = new Material(Shader.Find("Standard"));
                    defaultMaterial.color = Color.red;
                    defaultMaterial.SetFloat("_Metallic", 0f);
                    defaultMaterial.SetFloat("_Glossiness", 0.5f);
                    renderer.material = defaultMaterial;
                }
            }

            Debug.Log($"TurretEnemy: Created projectile with {damagePerProjectile} damage, {projectileLifetime}s lifetime");
            return projectile;
        }

        /// <summary>
        /// Public method to damage the turret. Call this from weapons/projectiles.
        /// </summary>
        /// <param name="damage">Amount of damage to deal</param>
        public void TakeDamage(float damage)
        {
            if (isDead) return;

            currentHealth -= damage;
            Debug.Log($"TurretEnemy: Took {damage} damage. Health: {currentHealth}/{maxHealth}");

            // Notify BloodSystem of damage
            if (bloodSystem != null)
            {
                Vector3 bloodPosition = GetBloodSpawnPosition();
                bloodSystem.OnEnemyDamaged(bloodPosition);
            }

            // Trigger damage animation if available
            if (animator != null)
                animator.SetTrigger("TakeDamage");

            if (currentHealth <= 0)
            {
                Die();
            }
        }

        private void Die()
        {
            if (isDead) return;

            isDead = true;
            Debug.Log("TurretEnemy: Destroyed!");

            // Notify BloodSystem of death
            if (bloodSystem != null)
            {
                Vector3 bloodPosition = GetBloodSpawnPosition();
                bloodSystem.OnEnemyKilled(bloodPosition);
            }

            // Spawn death effect
            if (deathEffectPrefab != null)
            {
                Instantiate(deathEffectPrefab, transform.position, Quaternion.identity);
            }

            // Play death sound
            PlayDeathSound();

            // Trigger death animation if available
            if (animator != null)
                animator.SetTrigger("Die");

            // Disable colliders
            Collider[] colliders = GetComponentsInChildren<Collider>();
            foreach (var collider in colliders)
            {
                collider.enabled = false;
            }

            // Destroy after delay to allow death effects to play
            StartCoroutine(DestroyAfterDelay(3f));
        }

        private IEnumerator DestroyAfterDelay(float delay)
        {
            yield return new WaitForSeconds(delay);

            if (gameObject != null)
            {
                Debug.Log("TurretEnemy: Removing from scene");
                Destroy(gameObject);
            }
        }

        private void SetupAudioSources()
        {
            // Create shoot audio source
            GameObject shootAudioObject = new GameObject("ShootAudioSource");
            shootAudioObject.transform.SetParent(transform);
            shootAudioSource = shootAudioObject.AddComponent<AudioSource>();
            shootAudioSource.playOnAwake = false;
            shootAudioSource.spatialBlend = 1f; // 3D sound

            // Create detection audio source
            GameObject detectionAudioObject = new GameObject("DetectionAudioSource");
            detectionAudioObject.transform.SetParent(transform);
            detectionAudioSource = detectionAudioObject.AddComponent<AudioSource>();
            detectionAudioSource.playOnAwake = false;
            detectionAudioSource.spatialBlend = 1f; // 3D sound

            // Create death audio source
            GameObject deathAudioObject = new GameObject("DeathAudioSource");
            deathAudioObject.transform.SetParent(transform);
            deathAudioSource = deathAudioObject.AddComponent<AudioSource>();
            deathAudioSource.playOnAwake = false;
            deathAudioSource.spatialBlend = 1f; // 3D sound
        }

        private void PlayShootSound()
        {
            if (shootSound != null && shootAudioSource != null)
            {
                shootAudioSource.clip = shootSound;
                shootAudioSource.volume = shootSoundVolume;
                shootAudioSource.Play();
            }
        }

        private void PlayDetectionSound()
        {
            if (detectionSound != null && detectionAudioSource != null)
            {
                detectionAudioSource.clip = detectionSound;
                detectionAudioSource.volume = detectionSoundVolume;
                detectionAudioSource.Play();
            }
        }

        private void PlayDeathSound()
        {
            if (deathSound != null && deathAudioSource != null)
            {
                deathAudioSource.clip = deathSound;
                deathAudioSource.volume = deathSoundVolume;
                deathAudioSource.Play();
            }
        }

        /// <summary>
        /// Get the best position for blood spawning (center of turret body)
        /// </summary>
        /// <returns>Position where blood should spawn</returns>
        private Vector3 GetBloodSpawnPosition()
        {
            // Try to get the center of the turret's collider for more accurate blood positioning
            Collider turretCollider = GetComponent<Collider>();
            if (turretCollider != null)
            {
                return turretCollider.bounds.center;
            }

            // Fallback to transform position with slight upward offset for body center
            return transform.position + Vector3.up * 0.5f;
        }

        // Visual feedback in editor
        void OnDrawGizmosSelected()
        {
            // Draw detection range
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(transform.position, detectionRadius);

            // Draw attack range
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, attackRange);

            // Draw projectile spawn point and preview projectile size
            if (projectileSpawnPoint != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawWireSphere(projectileSpawnPoint.position, 0.2f);
                Gizmos.DrawRay(projectileSpawnPoint.position, projectileSpawnPoint.forward * 2f);

                // Draw projectile size preview
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(projectileSpawnPoint.position, projectileSize);
            }
        }
    }
}
