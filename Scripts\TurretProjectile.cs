using UnityEngine;

namespace HELLSTRIKE
{
    [RequireComponent(typeof(Rigidbody))]
    [RequireComponent(typeof(Collider))]
    public class TurretProjectile : MonoBehaviour
    {
        [Header("Projectile Settings")]
        [Tooltip("Damage dealt to player on hit")]
        [SerializeField] private float damage = 15f;
        [Tooltip("How long projectile exists before auto-destroying")]
        [SerializeField] private float lifetime = 5f;
        [Toolt<PERSON>("Layer mask for what the projectile can hit")]
        [SerializeField] private LayerMask hitLayers = -1;

        [Header("Visual Effects")]
        [Tooltip("Particle effect to spawn on impact")]
        [SerializeField] private GameObject impactEffect;
        [Tooltip("How long impact effect lasts")]
        [SerializeField] private float impactEffectLifetime = 2f;

        [Header("Audio")]
        [Tooltip("Sound to play on impact")]
        [SerializeField] private AudioClip impactSound;
        [Tooltip("Volume of impact sound")]
        [Range(0f, 1f)]
        [SerializeField] private float impactSoundVolume = 0.8f;

        private Rigidbody rb;
        private bool hasHit = false;

        void Start()
        {
            rb = GetComponent<Rigidbody>();
            
            // Auto-destroy after lifetime
            Destroy(gameObject, lifetime);
            
            Debug.Log($"TurretProjectile: Created with {damage} damage, {lifetime}s lifetime");
        }

        void OnTriggerEnter(Collider other)
        {
            if (hasHit) return;

            // Check if we hit something on the hit layers
            if (((1 << other.gameObject.layer) & hitLayers) == 0) return;

            hasHit = true;

            Debug.Log($"TurretProjectile: Hit {other.gameObject.name}");

            // Check if we hit the player
            PlayerHealth playerHealth = other.GetComponent<PlayerHealth>();
            if (playerHealth != null)
            {
                playerHealth.TakeDamage(damage);
                Debug.Log($"TurretProjectile: Dealt {damage} damage to player");
            }

            // Spawn impact effects
            SpawnImpactEffect();
            PlayImpactSound();

            // Destroy the projectile
            Destroy(gameObject);
        }

        void OnCollisionEnter(Collision collision)
        {
            if (hasHit) return;

            // Check if we hit something on the hit layers
            if (((1 << collision.gameObject.layer) & hitLayers) == 0) return;

            hasHit = true;

            Debug.Log($"TurretProjectile: Collided with {collision.gameObject.name}");

            // Check if we hit the player
            PlayerHealth playerHealth = collision.gameObject.GetComponent<PlayerHealth>();
            if (playerHealth != null)
            {
                playerHealth.TakeDamage(damage);
                Debug.Log($"TurretProjectile: Dealt {damage} damage to player");
            }

            // Spawn impact effects at collision point
            if (collision.contacts.Length > 0)
            {
                SpawnImpactEffect(collision.contacts[0].point, collision.contacts[0].normal);
            }
            else
            {
                SpawnImpactEffect();
            }

            PlayImpactSound();

            // Destroy the projectile
            Destroy(gameObject);
        }

        private void SpawnImpactEffect()
        {
            SpawnImpactEffect(transform.position, -transform.forward);
        }

        private void SpawnImpactEffect(Vector3 position, Vector3 normal)
        {
            if (impactEffect != null)
            {
                GameObject effect = Instantiate(impactEffect, position, Quaternion.LookRotation(normal));
                Destroy(effect, impactEffectLifetime);
            }
        }

        private void PlayImpactSound()
        {
            if (impactSound != null)
            {
                // Create a temporary audio source to play the sound
                GameObject soundObject = new GameObject("TurretProjectileImpactSound");
                soundObject.transform.position = transform.position;
                
                AudioSource audioSource = soundObject.AddComponent<AudioSource>();
                audioSource.clip = impactSound;
                audioSource.volume = impactSoundVolume;
                audioSource.spatialBlend = 1f; // 3D sound
                audioSource.Play();

                // Destroy the sound object after the clip finishes playing
                float soundDuration = impactSound.length;
                Destroy(soundObject, soundDuration + 0.1f);
            }
        }

        /// <summary>
        /// Set the damage this projectile deals
        /// </summary>
        /// <param name="newDamage">Damage amount</param>
        public void SetDamage(float newDamage)
        {
            damage = newDamage;
        }

        /// <summary>
        /// Set the lifetime of this projectile
        /// </summary>
        /// <param name="newLifetime">Lifetime in seconds</param>
        public void SetLifetime(float newLifetime)
        {
            lifetime = newLifetime;
            
            // Cancel existing destruction and set new one
            CancelInvoke();
            Destroy(gameObject, lifetime);
        }
    }
}
