using UnityEngine;
using System.Collections;
using System.Collections.Generic;

namespace HELLSTRIKE
{
    public class BloodSystem : MonoBehaviour
    {
        [Header("Blood Particle Settings")]
        [SerializeField] private GameObject bloodParticlePrefab;
        [SerializeField] private float bloodLifetime = 5f;
        [SerializeField] private float healAmount = 10f;
        [SerializeField] private float bloodSpawnRadius = 2f;
        [SerializeField] private int bloodParticleCount = 5;
        
        [Header("Blood Sound Effects")]
        [SerializeField] private AudioClip[] bloodSounds = new AudioClip[3];
        [SerializeField] [Range(0f, 1f)] private float bloodSoundVolume = 0.7f;
        
        [Header("Healing Settings")]
        [SerializeField] private float healRadius = 1.5f;
        [SerializeField] private LayerMask playerLayer;
        [SerializeField] private bool enableHealingFeedback = true;
        [SerializeField] private Color healingFlashColor = Color.green;
        [SerializeField] private float healingFlashDuration = 0.3f;
        
        private AudioSource audioSource;
        private PlayerHealth playerHealth;
        private Camera playerCamera;
        private List<GameObject> activeBloodParticles = new List<GameObject>();
        
        private void Awake()
        {
            // Get or create audio source
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
            
            // Configure audio source for 3D sound
            audioSource.spatialBlend = 1f;
            audioSource.rolloffMode = AudioRolloffMode.Linear;
            audioSource.maxDistance = 20f;
            
            Debug.Log("BloodSystem: Initialized successfully");
        }
        
        private void Start()
        {
            // Find player components
            GameObject playerObject = GameObject.FindGameObjectWithTag("Player");
            if (playerObject != null)
            {
                playerHealth = playerObject.GetComponent<PlayerHealth>();
                playerCamera = playerObject.GetComponentInChildren<Camera>();
                
                if (playerHealth == null)
                {
                    Debug.LogError("BloodSystem: PlayerHealth component not found on player!");
                }
                
                Debug.Log($"BloodSystem: Found player components - Health: {playerHealth != null}, Camera: {playerCamera != null}");
            }
            else
            {
                Debug.LogError("BloodSystem: No GameObject with 'Player' tag found!");
            }
            
            // Subscribe to enemy death events
            SubscribeToEnemyEvents();
        }
        
        private void SubscribeToEnemyEvents()
        {
            // Find all enemy scripts and subscribe to their events
            ChaserEnemy[] chasers = FindObjectsOfType<ChaserEnemy>();
            ExplodingSpider[] spiders = FindObjectsOfType<ExplodingSpider>();
            Enemy_Patrol[] patrols = FindObjectsOfType<Enemy_Patrol>();
            TurretEnemy[] turrets = FindObjectsOfType<TurretEnemy>();

            Debug.Log($"BloodSystem: Found {chasers.Length} chasers, {spiders.Length} spiders, {patrols.Length} patrols, {turrets.Length} turrets");
            
            // Note: Since the enemy scripts don't have death events, we'll use a different approach
            // We'll check for enemy deaths in Update() method
            StartCoroutine(CheckForEnemyDeaths());
        }
        
        private IEnumerator CheckForEnemyDeaths()
        {
            List<GameObject> trackedEnemies = new List<GameObject>();
            
            // Get all enemies initially
            ChaserEnemy[] chasers = FindObjectsOfType<ChaserEnemy>();
            ExplodingSpider[] spiders = FindObjectsOfType<ExplodingSpider>();
            Enemy_Patrol[] patrols = FindObjectsOfType<Enemy_Patrol>();
            TurretEnemy[] turrets = FindObjectsOfType<TurretEnemy>();

            foreach (var chaser in chasers)
                if (chaser != null) trackedEnemies.Add(chaser.gameObject);
            foreach (var spider in spiders)
                if (spider != null) trackedEnemies.Add(spider.gameObject);
            foreach (var patrol in patrols)
                if (patrol != null) trackedEnemies.Add(patrol.gameObject);
            foreach (var turret in turrets)
                if (turret != null) trackedEnemies.Add(turret.gameObject);
            
            while (true)
            {
                // Check if any tracked enemies have been destroyed
                for (int i = trackedEnemies.Count - 1; i >= 0; i--)
                {
                    if (trackedEnemies[i] == null)
                    {
                        // Enemy was destroyed, but we don't have the position
                        // This approach won't work well, let's use a different method
                        trackedEnemies.RemoveAt(i);
                    }
                }
                
                yield return new WaitForSeconds(0.1f);
            }
        }
        
        /// <summary>
        /// Call this method when an enemy takes damage to spawn blood and play sound
        /// </summary>
        /// <param name="position">Position where the damage occurred</param>
        public void OnEnemyDamaged(Vector3 position)
        {
            Debug.Log($"BloodSystem: Enemy damaged at {position}, spawning blood");
            SpawnBloodParticles(position);
            PlayRandomBloodSound();
        }
        
        /// <summary>
        /// Call this method when an enemy dies to spawn blood particles
        /// </summary>
        /// <param name="position">Position where the enemy died</param>
        public void OnEnemyKilled(Vector3 position)
        {
            Debug.Log($"BloodSystem: Enemy killed at {position}, spawning blood");
            SpawnBloodParticles(position);
            PlayRandomBloodSound();
        }
        
        private void SpawnBloodParticles(Vector3 position)
        {
            if (bloodParticlePrefab == null)
            {
                Debug.LogWarning("BloodSystem: Blood particle prefab not assigned!");
                return;
            }

            for (int i = 0; i < bloodParticleCount; i++)
            {
                // Create blood particles closer to the enemy body
                Vector3 randomOffset = Random.insideUnitSphere * bloodSpawnRadius;

                // Adjust Y position to spawn around the enemy's center/torso area
                // Keep some particles at body level, some slightly above
                randomOffset.y = Random.Range(-0.5f, 1.5f);

                Vector3 spawnPosition = position + randomOffset;

                // Ensure blood doesn't spawn below ground
                if (spawnPosition.y < position.y - 0.5f)
                {
                    spawnPosition.y = position.y + Random.Range(0f, 0.5f);
                }

                // Spawn blood particle with random rotation for variety
                Quaternion randomRotation = Quaternion.Euler(
                    Random.Range(0f, 360f),
                    Random.Range(0f, 360f),
                    Random.Range(0f, 360f)
                );

                GameObject bloodParticle = Instantiate(bloodParticlePrefab, spawnPosition, randomRotation);

                // Add some physics for realistic blood movement
                Rigidbody rb = bloodParticle.GetComponent<Rigidbody>();
                if (rb == null)
                {
                    rb = bloodParticle.AddComponent<Rigidbody>();
                    rb.mass = 0.1f;
                    rb.linearDamping = 2f;
                }

                // Add random velocity for blood splatter effect
                Vector3 randomVelocity = Random.insideUnitSphere * 3f;
                randomVelocity.y = Mathf.Abs(randomVelocity.y) * 0.5f; // Reduce upward velocity
                rb.linearVelocity = randomVelocity;

                // Add BloodParticle component if it doesn't exist
                BloodParticle bloodComponent = bloodParticle.GetComponent<BloodParticle>();
                if (bloodComponent == null)
                {
                    bloodComponent = bloodParticle.AddComponent<BloodParticle>();
                }

                // Initialize the blood particle
                bloodComponent.Initialize(this, healAmount, healRadius, bloodLifetime);

                // Track the blood particle
                activeBloodParticles.Add(bloodParticle);

                // Clean up null references
                activeBloodParticles.RemoveAll(particle => particle == null);
            }

            Debug.Log($"BloodSystem: Spawned {bloodParticleCount} blood particles at {position}");
        }
        
        private void PlayRandomBloodSound()
        {
            if (bloodSounds == null || bloodSounds.Length == 0)
            {
                Debug.LogWarning("BloodSystem: No blood sounds assigned!");
                return;
            }
            
            // Filter out null audio clips
            List<AudioClip> validSounds = new List<AudioClip>();
            foreach (var sound in bloodSounds)
            {
                if (sound != null)
                    validSounds.Add(sound);
            }
            
            if (validSounds.Count == 0)
            {
                Debug.LogWarning("BloodSystem: All blood sound slots are empty!");
                return;
            }
            
            // Play random sound
            AudioClip randomSound = validSounds[Random.Range(0, validSounds.Count)];
            if (audioSource != null && randomSound != null)
            {
                audioSource.volume = bloodSoundVolume;
                audioSource.PlayOneShot(randomSound);
                Debug.Log($"BloodSystem: Playing blood sound: {randomSound.name}");
            }
        }
        
        /// <summary>
        /// Called by BloodParticle when player touches blood
        /// </summary>
        /// <param name="healAmount">Amount to heal</param>
        /// <param name="bloodParticle">The blood particle that was touched</param>
        public void OnBloodTouched(float healAmount, GameObject bloodParticle)
        {
            if (playerHealth != null)
            {
                Debug.Log($"BloodSystem: Player touched blood, healing for {healAmount}");
                playerHealth.Heal(healAmount);
                
                // Visual feedback
                if (enableHealingFeedback)
                {
                    StartCoroutine(HealingFlash());
                }
                
                // Remove the blood particle
                if (activeBloodParticles.Contains(bloodParticle))
                {
                    activeBloodParticles.Remove(bloodParticle);
                }
                
                Destroy(bloodParticle);
            }
        }
        
        private IEnumerator HealingFlash()
        {
            if (playerCamera == null) yield break;
            
            Color originalColor = playerCamera.backgroundColor;
            playerCamera.backgroundColor = healingFlashColor;
            yield return new WaitForSeconds(healingFlashDuration);
            playerCamera.backgroundColor = originalColor;
        }
        
        private void OnDrawGizmosSelected()
        {
            // Visualize blood spawn radius
            Gizmos.color = Color.red;
            Gizmos.DrawWireSphere(transform.position, bloodSpawnRadius);
            
            // Visualize heal radius
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position, healRadius);
        }
    }
    
    /// <summary>
    /// Component for individual blood particles
    /// </summary>
    public class BloodParticle : MonoBehaviour
    {
        private BloodSystem bloodSystem;
        private float healAmount;
        private float healRadius;
        private float lifetime;
        private bool hasHealed = false;
        
        public void Initialize(BloodSystem system, float heal, float radius, float life)
        {
            bloodSystem = system;
            healAmount = heal;
            healRadius = radius;
            lifetime = life;
            
            // Start lifetime countdown
            StartCoroutine(LifetimeCountdown());
            
            // Start checking for player collision
            StartCoroutine(CheckForPlayerCollision());
        }
        
        private IEnumerator LifetimeCountdown()
        {
            yield return new WaitForSeconds(lifetime);
            
            // Remove from tracking and destroy
            if (bloodSystem != null)
            {
                // The blood system will handle removal from its list
            }
            
            Destroy(gameObject);
        }
        
        private IEnumerator CheckForPlayerCollision()
        {
            while (!hasHealed && gameObject != null)
            {
                // Check for player in heal radius
                Collider[] hitColliders = Physics.OverlapSphere(transform.position, healRadius);
                
                foreach (var collider in hitColliders)
                {
                    if (collider.CompareTag("Player") && !hasHealed)
                    {
                        hasHealed = true;
                        bloodSystem?.OnBloodTouched(healAmount, gameObject);
                        yield break;
                    }
                }
                
                yield return new WaitForSeconds(0.1f);
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            // Visualize heal radius
            Gizmos.color = Color.green;
            Gizmos.DrawWireSphere(transform.position, healRadius);
        }
    }
}
